// Vite plugin to copy techpana.widget.min.js to dist after build
import { promises as fs } from 'fs';
import path from 'path';
import { minify } from 'terser';

export default function CopyWidgetScriptPlugin() {
  return {
    name: 'copy-widget-script',
    closeBundle: async () => {
      const src = path.resolve(__dirname, 'src/techpana.widget.main.js');
      const dest = path.resolve(__dirname, 'dist/techpana.widget.min.js');
      try {
        const code = await fs.readFile(src, 'utf8');
        const minified = await minify(code, { format: { comments: false } });
        await fs.writeFile(dest, minified.code, 'utf8');
        // eslint-disable-next-line no-console
        console.log('Bundled and minified techpana.widget.min.js to dist.');
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error('Failed to bundle/minify widget script:', err);
      }
    },
  };
}
