import React from 'react';
import { useState, useEffect } from 'react';
import AiSummaryIcon from './components/icons/AiSummaryIcon';
import ForYouIcon from './components/icons/ForYouIcon';
import NewsDetailsDialog from './components/NewsDetailsDialog';
import NewsWidget from './components/NewsWidget';
import axios from 'axios';
import trackEvent from './utils/trackEvent';
// import ForYouIcon from '../components/icons/ForYouIcon';
// import NewsWidget from '../components/NewsWidget';
// import NewsDetailsDialog from '../components/NewsDetailsDialog';
// import AiSummaryIcon from '@/components/icons/AiSummaryIcon';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
  post_id: string;
}

interface NewsData {
  post_id: string;
  main_post_summary: string;
  related_posts: RelatedPost[];
}

const Index = () => {
  const [widgetMode, setWidgetMode] = useState<'summary' | 'related' | null>(null);
  const [newsData, setNewsData] = useState<NewsData | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [shouldShowWidget, setShouldShowWidget] = useState(false);

  // State for details dialog
  const [selectedPostIndex, setSelectedPostIndex] = useState<number | null>(null);
  const [expandedQAs, setExpandedQAs] = useState<string[]>([]);
  const [isDetailsClosing, setIsDetailsClosing] = useState(false);

  // State for widget closing animation
  const [isWidgetClosing, setIsWidgetClosing] = useState(false);
  const [isWidgetOpening, setIsWidgetOpening] = useState(false);

  const path = window.location.pathname;
  const postId = path.split('/')[2];

  // Fetch news data from API on page load
  const fetchNewsData = async (mode?: 'summary' | 'related') => {
    try {
      const apiUrl = `https://techpana-ai.nextai.asia/v1/get_report/${postId}`;

      const newsDataResponse = await axios.get<NewsData>(apiUrl);
      if (newsDataResponse.status === 200 && newsDataResponse.data) {
        const { data } = newsDataResponse;
        setNewsData(data);
        setShouldShowWidget(true);
        // Only set widget mode if explicitly requested (when user clicks)
        if (mode) {
          setWidgetMode(mode);
        }
      } else {
        throw new Error(`API request failed with status: ${newsDataResponse.status}`);
      }
    } catch (error) {
      // console.error('Error fetching news data:', error);
      setNewsData(null);
      setShouldShowWidget(false);
    } finally {
      setIsLoadingData(false);
    }
  };

  // Fetch data on component mount (but don't open widget)
  useEffect(() => {
    fetchNewsData(); // Load data without opening widget
    // trackEvent('post_viewed', postId);
  }, []);



  // Callback functions for ForYouWidget and NewsDetailsDialog
  const handleTogglePost = (index: number) => {
    if (selectedPostIndex === index) {
      handleCloseDetails();
    } else {
      // Reset closing state when opening a new post
      setIsDetailsClosing(false);
      setSelectedPostIndex(index);
      setExpandedQAs(prevQAs => prevQAs.filter(qa => qa.startsWith(`${index}-`)));
    }
  };

  const handleToggleQA = (questionId: string) => {
    setExpandedQAs(prev => {
      if (prev.includes(questionId)) {
        return prev.filter(qa => qa !== questionId);
      } else {
        return [...prev, questionId];
      }
    });
  };

  const handleCloseDetails = () => {
    setIsDetailsClosing(true);
    setTimeout(() => {
      setSelectedPostIndex(null);
      setExpandedQAs([]);
      setIsDetailsClosing(false);
    }, 500); // Match the transition duration
  };

  const handleCloseWidget = () => {
    // If widget is currently opening, wait for it to complete before closing
    const delay = isWidgetOpening ? 100 : 50;

    setTimeout(() => {
      setIsWidgetOpening(false); // Stop any opening animation
      setIsWidgetClosing(true);
      setTimeout(() => {
        setWidgetMode(null);
        setIsWidgetClosing(false);
        // Close any open details when closing the For You widget
        if (widgetMode === 'related') {
          handleCloseDetails();
        }
      }, 300); // Match the NewsWidget animation duration
    }, delay);
  };

  const handleOpenPost = (url: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setTimeout(() => {
      window.open(url, '_blank');
    }, 100);
  };

  return (
    <div className="relative">
      {shouldShowWidget && (
        <>
          {/* Control Icons - always show */}
          <div className="fixed bottom-16 right-8 md:bottom-12 md:right-12 z-50 flex flex-col gap-3 sm:gap-4 p-2 pt-4 rounded-full backdrop-blur-md bg-green-200/20 border border-gray-300/60 shadow">

            {/* AI Summary Button */}
            <div className="relative group">
              <button
                onClick={() => {
                  if (widgetMode === 'summary') {
                    handleCloseWidget();
                  } else if (!isLoadingData) {
                    if (newsData) {
                      setIsWidgetOpening(true);
                      setWidgetMode('summary');
                      // Clear opening state after animation completes
                      setTimeout(() => setIsWidgetOpening(false), 500);
                    } else {
                      fetchNewsData('summary');
                    }
                  }
                }}
                disabled={isLoadingData}
                className={`relative rounded-full shadow-2xl transition-all duration-500 hover:scale-110
                  text-white pulse-glow float-animation active:scale-95 group-hover:rotate-12 touch-target mobile-button
                  p-3 md:p-5 min-w-[48px] min-h-[48px] md:min-w-[64px] md:min-h-[64px]
                  ${isLoadingData
                    ? 'bg-gradient-to-br from-gray-400 to-gray-500 cursor-not-allowed'
                    : 'bg-gradient-to-br from-green-500 to-green-600 hover:shadow-[0_0_40px_rgba(34,197,94,1)]'
                  }`}
              >
                {isLoadingData ? (
                  <div className="w-5 h-5 md:w-7 md:h-7 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <AiSummaryIcon size={20} className="w-5 h-5 md:w-7 md:h-7" />
                )}
              </button>

              {/* Tooltip */}
              <div className="absolute right-full mr-3 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out pointer-events-none hidden sm:block z-[60] group-hover:scale-105">
                <div className="relative bg-gradient-to-br from-green-600 to-green-600 text-white px-3 py-2 rounded-2xl text-sm font-semibold whitespace-nowrap shadow-2xl border border-green-400/30 backdrop-blur-sm min-h-[26px] flex items-center">
                  <span className="inline-block leading-none">{isLoadingData ? 'Loading...' : 'AI Summary'}</span>
                  {/* Seamless arrow */}
                  <div className="absolute left-full top-1/2 -translate-y-1/2 -ml-px">
                    <div className="border-[6px] border-transparent border-l-green-600"></div>
                  </div>
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-green-500/20 rounded-2xl blur-sm -z-10"></div>
                </div>
              </div>
            </div>

            {/* For You Button */}
            <div className="relative group">
              <button
                onClick={() => {
                  if (widgetMode === 'related') {
                    handleCloseWidget();
                  } else if (!isLoadingData) {
                    if (newsData) {
                      setIsWidgetOpening(true);
                      setWidgetMode('related');
                      handleCloseDetails();
                      // Clear opening state after animation completes
                      setTimeout(() => setIsWidgetOpening(false), 500);
                    } else {
                      fetchNewsData('related');
                      handleCloseDetails();
                    }
                  }
                }}
                disabled={isLoadingData}
                className={`relative rounded-full shadow-2xl transition-all duration-500 hover:scale-110
                  text-white pulse-glow float-animation active:scale-95 group-hover:rotate-12 touch-target mobile-button
                  p-3 md:p-5 min-w-[48px] min-h-[48px] md:min-w-[64px] md:min-h-[64px]
                  ${isLoadingData
                    ? 'bg-gradient-to-br from-gray-400 to-gray-500 cursor-not-allowed'
                    : 'bg-gradient-to-br from-yellow-500 to-yellow-600 hover:shadow-[0_0_40px_rgba(251,191,36,1)]'
                  }`}
                style={{ animationDelay: '1s' }}
              >
                {isLoadingData ? (
                  <div className="w-5 h-5 md:w-6 md:h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <ForYouIcon size={20} className="w-5 h-5 md:w-7 md:h-7 drop-shadow-lg" />
                )}
              </button>

              {/* Tooltip */}
              <div className="absolute right-full mr-3 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out pointer-events-none hidden sm:block z-[60] group-hover:scale-105">
                <div className="relative bg-gradient-to-br from-yellow-600 to-yellow-600 text-white px-3 py-2 rounded-2xl text-sm font-semibold whitespace-nowrap shadow-2xl border border-yellow-400/30 backdrop-blur-sm min-h-[26px] flex items-center">
                  <span className="inline-block leading-none">{isLoadingData ? 'Loading...' : 'Recommended For You'}</span>
                  {/* Seamless arrow */}
                  <div className="absolute left-full top-1/2 -translate-y-1/2 -ml-px">
                    <div className="border-[6px] border-transparent border-l-yellow-600"></div>
                  </div>
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/20 to-yellow-500/20 rounded-2xl blur-sm -z-10"></div>
                </div>
              </div>
            </div>
          </div>
          {/* NewsDetailsDialog - Large screens: side-by-side positioning */}
          {widgetMode === 'related' && newsData && (selectedPostIndex !== null || isDetailsClosing) && (
            <div
              className={`fixed top-1/2 -translate-y-1/2 z-[10000] hidden xl:block transition-all duration-500 ease-out ${
                selectedPostIndex !== null && !isDetailsClosing
                  ? 'opacity-100 translate-x-0 scale-100'
                  : 'opacity-0 -translate-x-8 scale-95 pointer-events-none'
              }`}
              style={{
                // Position to the left of NewsWidget with 26px gap
                right: 'calc(min(95vw, 580px) + 26px)',
                width: 'min(500px, calc(100vw - min(95vw, 400px) - 52px))', // Wider
                maxHeight: '85vh', // Taller
                transitionDelay: selectedPostIndex !== null && !isDetailsClosing ? '50ms' : '0ms'
              }}
            >
              <NewsDetailsDialog
                selectedPost={selectedPostIndex !== null ? newsData.related_posts[selectedPostIndex] : null}
                selectedPostIndex={selectedPostIndex}
                expandedQAs={expandedQAs}
                onClose={handleCloseDetails}
                onToggleQA={handleToggleQA}
                onOpenPost={handleOpenPost}
              />
            </div>
          )}

          {/* NewsDetailsDialog - Mobile screens: centered and scrollable */}
          {widgetMode === 'related' && newsData && (selectedPostIndex !== null || isDetailsClosing) && (
            <div
              className={`fixed inset-0 z-[10000] xl:hidden flex items-center justify-center px-4 transition-all duration-500 ease-out ${
                selectedPostIndex !== null && !isDetailsClosing
                  ? 'opacity-100 scale-100'
                  : 'opacity-0 scale-90 pointer-events-none'
              }`}
              style={{
                transitionDelay: selectedPostIndex !== null && !isDetailsClosing ? '50ms' : '0ms',
              }}
            >
              <div className="bg-white rounded-xl shadow-lg w-full max-w-[450px] max-h-[90vh] overflow-y-auto">
                <NewsDetailsDialog
                  selectedPost={selectedPostIndex !== null ? newsData.related_posts[selectedPostIndex] : null}
                  selectedPostIndex={selectedPostIndex}
                  expandedQAs={expandedQAs}
                  onClose={handleCloseDetails}
                  onToggleQA={handleToggleQA}
                  onOpenPost={handleOpenPost}
                />
              </div>
            </div>
          )}



          <NewsWidget
            mode={widgetMode}
            onClose={handleCloseWidget}
            selectedPostIndex={selectedPostIndex}
            onTogglePost={handleTogglePost}
            onOpenPost={handleOpenPost}
            newsData={newsData}
            isClosing={isWidgetClosing}
            isOpening={isWidgetOpening}
          />
        </>
      )}
    </div>
  );
};

export default Index;
