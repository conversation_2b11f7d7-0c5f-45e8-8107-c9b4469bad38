import axios from "axios";

// In-memory Set to track events already fired
const firedEvents = new Set<string>();

const path = window.location.pathname;
const parentPostId = path.split('/')[2];

export default function trackEvent(eventName: EventNames, postId: string) {
  if (!postId) return;

  const key = `${eventName}_${postId}`;
  if (firedEvents.has(key)) return;

  firedEvents.add(key);

  axios.post(`https://techpana-ai.nextai.asia/v1/log-event?event=${eventName}&post_id=${postId}&parent_post_id=${postId === parentPostId ? null : parentPostId}`, {})
    .catch(err => {
      console.warn("Event tracking failed:", err);
    });
}

type EventNames =
  | "post_viewed"
  | "ai_summary_button_clicked"
  | "for_you_button_clicked"
  | "view_post_details"
  | "visit_post_from_for_you"
  | "visit_post_from_news_details"
  | "generate_qna";
