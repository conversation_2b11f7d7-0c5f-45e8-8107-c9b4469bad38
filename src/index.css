/* @tailwind base;
@tailwind components;
@tailwind utilities; */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom Widget Animations */
@layer utilities {
  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .float-animation {
    animation: float 3s ease-in-out infinite;
  }

  .slide-in-right {
    animation: slide-in-right 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .slide-out-right {
    animation: slide-out-right 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19);
  }

  .scale-bounce {
    animation: scale-bounce 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .fade-in-up {
    animation: fade-in-up 0.5s ease-out;
  }

  .shimmer {
    background: linear-gradient(90deg, rgb(240,240,240), rgb(255,255,255), rgb(240,240,240));
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .bg-background {
    background-color: hsl(var(--background));
  }
  .text-foreground {
    color: hsl(var(--foreground));
  }
  .border-border {
    border-color: hsl(var(--border));
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 1);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 30px rgba(34, 197, 94, 1);
    transform: scale(1.05);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slide-in-right {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out-right {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes scale-bounce {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fade-in-up {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Custom Scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgb(110, 227, 94) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgb(110, 227, 94), rgb(90, 200, 80));
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgb(90, 200, 80), rgb(70, 180, 60));
  background-clip: content-box;
}

/* Hover effects for interactive elements */
.interactive-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Advanced animations */
.stagger-animation {
  animation: stagger-fade-in 0.6s ease-out forwards;
}

.bounce-scale {
  animation: bounce-scale 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.rotate-in {
  animation: rotate-in 0.6s ease-out;
}

.slide-in-left {
  animation: slide-in-left 0.5s ease-out;
}

.gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.widget-entrance {
  animation: widget-entrance 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.dialog-fade-in {
  animation: dialog-fade-in 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.dialog-fade-out {
  animation: dialog-fade-out 0.5s cubic-bezier(0.55, 0.06, 0.68, 0.19);
}

@keyframes stagger-fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes bounce-scale {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes rotate-in {
  0% {
    transform: rotate(-180deg) scale(0.5);
    opacity: 0;
  }
  100% {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
}

@keyframes slide-in-left {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes widget-entrance {
  0% {
    transform: translateY(100%) scale(0.8);
    opacity: 0;
  }
  60% {
    transform: translateY(-10px) scale(1.02);
    opacity: 0.9;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes dialog-fade-in {
  0% {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes dialog-fade-out {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .mobile-optimized {
    padding: 1rem;
  }

  .mobile-text {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .mobile-button {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem;
  }

  .mobile-spacing {
    gap: 0.75rem;
  }

  .mobile-header {
    padding: 1rem;
    font-size: 1.125rem;
  }

  .mobile-content {
    max-height: calc(100vh - 8rem);
    overflow-y: auto;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .hover\:scale-110:hover {
    transform: scale(1.05);
  }

  .interactive-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
}

/* Content-responsive widget sizing */
.content-responsive {
  /* Dynamic sizing based on content */
  display: flex;
  flex-direction: column;
  position: fixed !important;
  overflow: hidden;
  z-index: 9999 !important;
}

@media (min-width: 640px) {
  .content-responsive {
    min-width: 400px;
    max-width: 500px;
    max-height: calc(100vh - 4rem);
  }
}

@media (min-width: 768px) {
  .content-responsive {
    min-width: 450px;
    max-width: 600px;
  }
}

@media (min-width: 1024px) {
  .content-responsive {
    min-width: 500px;
    max-width: 700px;
    max-height: calc(100vh - 8rem);
  }
}

@media (min-width: 1280px) {
  .content-responsive {
    max-width: 800px;
  }
}

@media (min-width: 1536px) {
  .content-responsive {
    max-width: 900px;
  }
}

/* Adaptive content containers */
.adaptive-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.adaptive-content > * {
  flex-shrink: 0;
}

/* Smart scrolling for content overflow */
.smart-scroll {
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgb(156, 163, 175) transparent;
}

.smart-scroll::-webkit-scrollbar {
  width: 6px;
}

.smart-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.smart-scroll::-webkit-scrollbar-thumb {
  background: rgb(156, 163, 175);
  border-radius: 3px;
}

.smart-scroll::-webkit-scrollbar-thumb:hover {
  background: rgb(120, 130, 140);
}

/* Floating widget override */
.floating-widget {
  position: fixed !important;
  z-index: 9999 !important;
  pointer-events: auto !important;
}

/* Content-specific responsive sizing */
.widget-summary {
  /* Optimized for summary content - 600px width */
  width: min(95vw, 600px);
  height: min(800px, 80vh);
}

.widget-related {
  /* Optimized for related posts - 600px width */
  width: min(95vw, 600px);
  height: min(800px, 80vh);
}

/* Responsive breakpoints for widget content */
@media (max-width: 640px) {
  .widget-summary,
  .widget-related {
    /* Mobile: Full width with margins */
    width: calc(100vw - 2rem) !important;
    max-width: calc(100vw - 2rem) !important;
  }

  .widget-summary {
    /* Mobile: Max height for summary */
    max-height: calc(100vh - 4rem) !important;
    max-width: calc(100vw - 4rem) !important;
        margin-right: -2rem;
        /* margin-left: 0.5rem; */
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .widget-summary,
  .widget-related {
    /* Small tablets: 90% width, max 600px */
    width: min(90vw, 600px) !important;
  }
}

@media (min-width: 769px) {
  .widget-summary,
  .widget-related {
    /* Desktop and larger: Fixed 600px width */
    width: 600px !important;
  }
}