(function () {
  'use strict';

  // Inject stylesheet
  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = 'https://cdn.nextai.asia/dist/techpana.widget.style.min.css';
  document.head.appendChild(link);

  // Inject script and initialize after loading
  const script = document.createElement('script');
  script.src = 'https://cdn.nextai.asia/dist/techpana.widget.iife.min.js';
  script.onload = () => {
    if (typeof Widget !== 'undefined' && typeof Widget.init === 'function') {
      Widget.init();
    }
  };
  document.body.appendChild(script);
})();
