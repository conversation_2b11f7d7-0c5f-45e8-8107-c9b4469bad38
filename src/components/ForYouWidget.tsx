import React from 'react';
import { ExternalLink } from 'lucide-react';
import trackEvent from '../utils/trackEvent';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
  post_id: string;
}

interface ForYouWidgetProps {
  relatedPosts: RelatedPost[];
  selectedPostIndex: number | null;
  onTogglePost: (index: number) => void;
  onOpenPost: (url: string, e: React.MouseEvent) => void;
}

const ForYouWidget: React.FC<ForYouWidgetProps> = ({
  relatedPosts,
  selectedPostIndex,
  onTogglePost,
  onOpenPost
}) => {
  // Function to get random 5 posts from the available posts
  const getRandomPosts = (posts: RelatedPost[], count: number = 5) => {
    if (posts.length <= count) return posts;
    const shuffled = [...posts].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  };

  const displayPosts = getRandomPosts(relatedPosts);

  return (
    <div className="space-y-4">
      {displayPosts.map((post, index) => (
        <div
          key={index}
          className={`relative bg-white rounded-2xl overflow-hidden shadow-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-2xl cursor-pointer border-2 ${
            selectedPostIndex === index
              ? 'border-green-400 shadow-xl ring-2 ring-green-300/50'
              : 'border-gray-100 hover:border-green-200'
          }`}
          style={{ animation: `fadeInUp 0.5s ease-out ${index * 0.1}s both` }}
          onClick={() => {
            onTogglePost(index);
            trackEvent('view_post_details', post.post_id);
          }}
        >
          {/* Post Header */}
          <div className="p-3 sm:p-4">
            <div className="flex items-start gap-3">
              <div className="flex-1">
                {/* Title */}
                <h4 className="font-semibold text-gray-800 text-base sm:text-lg leading-tight line-clamp-2 mb-3 transition-colors group-hover:text-green-600">
                  {post.post_title}
                </h4>

                {/* Read Full Article Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    trackEvent('visit_post_from_for_you', post.post_id);
                    onOpenPost(post.post_url, e);
                  }}
                  className="inline-flex items-center gap-2 px-3 py-2 bg-green-100 hover:bg-green-200 text-green-800 rounded-lg transition-colors text-sm font-medium"
                >
                  <ExternalLink size={16} />
                  Read Full Article
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}

      {relatedPosts.length === 0 && (
        <div className="text-center py-16 px-6 bg-gradient-to-b from-gray-50 to-white rounded-2xl shadow-md">
          <div className="relative mb-6 mx-auto w-20 h-20 bg-gradient-to-br from-green-100 to-teal-100 rounded-full flex items-center justify-center shadow-lg">
            <ExternalLink size={28} className="text-green-500" />
            <div className="absolute -top-2 -right-2 w-5 h-5 bg-green-300/50 rounded-full animate-pulse"></div>
          </div>
          <h3 className="text-gray-700 text-xl font-bold mb-3">No Related Posts Found</h3>
          <p className="text-gray-500 text-sm max-w-sm mx-auto leading-relaxed">
            We're constantly curating fresh content. Check back soon for personalized recommendations!
          </p>
        </div>
      )}

      {/* CSS Animation Keyframes */}
      <style>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
};

export default ForYouWidget;