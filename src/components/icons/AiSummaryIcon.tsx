import React from 'react';
import type { SVGProps } from 'react';

interface AiSummaryIconProps extends SVGProps<SVGSVGElement> {
  size?: number;
}

export const AiSummaryIcon = ({ size = 14, ...props }: AiSummaryIconProps) => {
  return (<svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 12 12" {...props}><path fill="currentColor" d="M0 3.002c0-.116.073-.218.186-.254q.103-.034.215-.068c.463-.142.985-.301 1.132-.813l.215-.684c.07-.244.44-.244.51 0l.222.695c.102.306.35.553.652.655l.685.215c.244.069.244.44 0 .509c-.075.028-.168.055-.268.085c-.298.088-.663.197-.821.384c-.188.16-.3.537-.39.836a4 4 0 0 1-.08.252a.27.27 0 0 1-.251.186a.27.27 0 0 1-.255-.182l-.23-.684a1.06 1.06 0 0 0-.655-.659l-.681-.218A.27.27 0 0 1 0 3.002M.76 5.03a1.001 1.001 0 0 0-.143 1.894A1 1 0 0 0 1 7v3.91c0 .602.488 1.09 1.09 1.09h7.82A1.09 1.09 0 0 0 11 10.91V7a1 1 0 0 0 0-2v-.91A1.09 1.09 0 0 0 9.91 3H7v-.955a.545.545 0 0 0-.545-.545h-.91A.545.545 0 0 0 5 2.045v.958c0 .451-.262 1.022-.89 1.211l-.672.215a.1.1 0 0 0-.022.02v.002l-.207.656c-.187.63-.758.893-1.211.893C1.55 6 .99 5.744.794 5.134zM7 7a1 1 0 1 1 2 0a1 1 0 0 1-2 0M4.5 9h3v1h-3zM5 7a1 1 0 1 1-2 0a1 1 0 0 1 2 0"></path></svg>);
}

export default AiSummaryIcon;