import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import ForYouIcon from './icons/ForYouIcon';
import NewsSummaryWidget from './NewsSummaryWidget';
import ForYouWidget from './ForYouWidget';
import AiSummaryIcon from './icons/AiSummaryIcon';
import nextaiLogo from '../assets/nextai-logo.png';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
  post_id: string;
}

interface NewsData {
  post_id: string;
  main_post_summary: string;
  related_posts: RelatedPost[];
}

interface NewsWidgetProps {
  mode: 'summary' | 'related' | null;
  onClose: () => void;
  selectedPostIndex?: number | null;
  onTogglePost?: (index: number) => void;
  onOpenPost?: (url: string, e: React.MouseEvent) => void;
  newsData: NewsData | null; // Add newsData prop
  isClosing?: boolean; // Add isClosing prop from parent
  isOpening?: boolean; // Add isOpening prop from parent
}

const NewsWidget: React.FC<NewsWidgetProps> = ({
  mode,
  onClose,
  selectedPostIndex,
  onTogglePost,
  onOpenPost,
  newsData, // Receive newsData from parent
  isClosing: externalIsClosing = false, // Receive isClosing from parent
  isOpening: externalIsOpening = false // Receive isOpening from parent
}) => {
  const [internalIsClosing, setInternalIsClosing] = useState(false);

  // Use external isClosing if provided, otherwise use internal state
  const isClosing = externalIsClosing || internalIsClosing;
  const contentRef = React.useRef<HTMLDivElement>(null);



  // Calculate optimal widget size based on content
  const getOptimalWidth = () => {
    // Fixed 600px width for summary, 400px for related mode, responsive on smaller screens
    return mode === 'summary' ? 'min(95vw, 600px)' : 'min(95vw, 400px)';
  };

  const getOptimalHeight = () => {
    // For summary mode, fit content with max height
    if (mode === 'summary') {
      return 'auto';
    }
    return 'min(800px, 90vh)'; // Fixed height for related posts
  };

  const handleClose = () => {
    setInternalIsClosing(true);
    setTimeout(() => {
      onClose();
      setInternalIsClosing(false);
    }, 300);
  };

  if (!mode || !newsData) return null;

  return (
    <>
      {/* Widget Container */}
      <div
        className={`fixed z-[9999]
                    bottom-48 right-16 md:bottom-60 sm:right-8 md:right-12
                   bg-white/95 backdrop-blur-md shadow-2xl border border-gray-200
                   transform transition-all duration-500 ease-out
                   ${isClosing
                     ? (mode === 'summary'
                        ? 'translate-y-full opacity-0 scale-95'
                        : 'translate-x-full opacity-0')
                     : mode
                     ? (mode === 'summary'
                        ? 'translate-y-0 opacity-100 scale-100 widget-entrance'
                        : 'translate-x-0 opacity-100 slide-in-right')
                     : (mode === 'summary'
                        ? 'translate-y-full opacity-0 scale-95'
                        : 'translate-x-full opacity-0')
                   }
                   content-responsive  floating-widget ${mode === 'summary' ? 'widget-summary rounded-xl sm:rounded-2xl flex flex-col' : 'widget-related h-full flex flex-col'}`}
        style={{
          ...(mode === 'summary' ? {
            // bottom: '240px', // Position above the icon with more clearance (icon is at bottom: 24px + icon height ~80px + gap)
            // right: '64px',
            width: getOptimalWidth(),
            height: getOptimalHeight(),
            maxHeight: 'calc(100vh - 200px)', // Leave space for icon below
            minHeight: 'auto',
          } : {
            top: 0,
            right: 0,
            width: getOptimalWidth(),
            height: '100vh',
            maxHeight: '100vh',
            minHeight: '100vh',
          }),
          background: mode === 'summary'
            ? 'linear-gradient(135deg, rgba(34,197,94,0.02) 0%, rgba(255,255,255,0.9) 20%, rgba(249,250,251,0.95) 100%)'
            : 'linear-gradient(135deg, rgba(34,197,94,0.02) 0%, rgba(255,255,255,0.9) 20%, rgba(249,250,251,0.95) 100%)',
          boxShadow: mode === 'summary'
            ? '0 25px 50px -12px rgba(34, 197, 94, 0.15), 0 0 0 2px rgba(34, 197, 94, 0.15), 0 8px 32px -8px rgba(0, 0, 0, 0.2)'
            : '0 25px 50px -12px rgba(34, 197, 94, 0.15), 0 0 0 2px rgba(34, 197, 94, 0.15), 0 8px 32px -8px rgba(0, 0, 0, 0.2)'
        }}
      >
        {/* Modern Header */}
        <div className={`relative p-3 pl-4 sm:p-6 pb-2 sm:pb-3 flex justify-between items-start border-b ${
          mode === 'summary' ? 'border-gray-300/80' : 'border-gray-300/80'
        }`}>
          <div className="flex items-center gap-3">
            <div className={`p-3 rounded-xl shadow-sm ${
              mode === 'summary'
                ? 'bg-gradient-to-br from-green-500 to-green-600 text-white'
                : 'bg-gradient-to-br from-yellow-500 to-yellow-600 text-white'
            }`}>
              {mode === 'summary' ? (
                // <Sparkles className="w-4 h-4 sm:w-5 sm:h-5" />
                <AiSummaryIcon size={20} className="sm:w-5 sm:h-5" />
              ) : (
                <ForYouIcon size={20} className="sm:w-5 sm:h-5" />
              )}
            </div>
            <div>
              <h3 className={`font-bold text-base sm:text-2xl ${
                mode === 'summary' ? 'text-gray-800' : 'text-gray-800'
              }`}>
                {mode === 'summary' ? 'AI Summary' : 'Recommended For You'}
              </h3>
              {/* {mode !== 'summary' && (
                <p className={`text-xs sm:text-sm text-green-600`}>
                  <span>
                    {newsData?.related_posts?.length || 0} related articles
                  </span>
                </p>
              )} */}
            </div>
          </div>

          <button
            onClick={handleClose}
            className={`p-2 rounded-lg transition-all duration-200 hover:rotate-90 touch-target origin-center ${
              mode === 'summary'
                ? 'hover:bg-green-50 text-gray-400 hover:text-green-600'
                : 'hover:bg-green-50 text-gray-400 hover:text-green-600'
            }`}
            title="Close"
          >
            <X size={16} className="sm:w-4 sm:h-4" />
          </button>
        </div>

        {/* Content */}
        <div
          ref={contentRef}
          className={mode === 'summary' ? 'overflow-hidden flex-1' : 'flex-1 overflow-hidden'}
        >
          <div className={mode === 'summary' ? 'smart-scroll mobile-content' : 'h-full smart-scroll mobile-content'}>
            <div className={mode === 'summary' ? 'relative' : 'relative min-h-[200px]'}>
              <div className="p-4 sm:p-6 mobile-optimized adaptive-content transition-all duration-300">
                {mode === 'summary' && (
                  <NewsSummaryWidget
                    summary={newsData.main_post_summary}
                    postId={newsData.post_id}
                    isExpanded={true}
                    onToggle={() => {}}
                    />
                  )}

                  {mode === 'related' && (
                    <ForYouWidget
                      relatedPosts={newsData.related_posts}
                      selectedPostIndex={selectedPostIndex !== undefined ? selectedPostIndex : null}
                      onTogglePost={onTogglePost || (() => {})}
                      onOpenPost={onOpenPost || (() => {})}
                    />
                  )}
                </div>
            </div>
          </div>
        </div>

        {/* Footer - Show for both summary and related modes */}
        <div className={`p-3 sm:p-6 -mt-4`}>
          <div className="flex items-center justify-center">
            <div className={`flex items-center gap-2 text-xs sm:text-[14px] text-black font-bold ${mode === 'related' ? 'pt-3' : ''}`}>
              <span>Powered by</span>
              <a
                className="hover:opacity-80 transition-all"
                target="_blank"
                href="https://nextai.asia"
                rel="noopener noreferrer"
              >
                <img
                  src={nextaiLogo}
                  alt="NextAI"
                  className="h-4 sm:h-[18px] w-auto inline-block"
                  title="NextAI"
                />
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default NewsWidget;
