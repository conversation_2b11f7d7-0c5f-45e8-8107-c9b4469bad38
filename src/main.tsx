import './index.css'
import React from 'react'
import { createRoot } from 'react-dom/client'
import WidgetEntry from './WidgetEntry'

if (import.meta.env.DEV) {
  const container = document.getElementById('root')
  if (container) {
    const root = createRoot(container)
    root.render(
      <React.StrictMode>
        <WidgetEntry />
      </React.StrictMode>
    )
  }
}

let container: HTMLDivElement | null = null

function init(config: { bottom?: number; right?: number, left?: number, top?: number } = {}) {
  if (container) return
  container = document.createElement('div')
  const style: Partial<CSSStyleDeclaration> = {
    position: 'fixed',
    zIndex: '9999'
  };

  if (config.bottom !== undefined) style.bottom = `${config.bottom}px`;
  if (config.right !== undefined) style.right = `${config.right}px`;
  if (config.left !== undefined) style.left = `${config.left}px`;
  if (config.top !== undefined) style.top = `${config.top}px`;

  // Default position if no properties are provided
  if (Object.keys(config).length === 0) {
    style.bottom = '20px';
    style.right = '20px';
  }

  Object.assign(container.style, style);
  document.body.appendChild(container)

  const root = createRoot(container)
  root.render(<WidgetEntry />)
}

;(window as any).Widget = { init }