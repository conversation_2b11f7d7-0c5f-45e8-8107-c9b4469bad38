// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import CopyWidgetScriptPlugin from "./copyWidgetScriptPlugin";

export default defineConfig(({ command }) => {
  const reactPlugin = react({
    jsxRuntime: "automatic", // Use automatic runtime for correct prod build
  });
  if (command === "serve") {
    return {
      plugins: [reactPlugin],
      server: {
        allowedHosts: ["cdn.nextai.asia"],
        proxy: {
          "/api": {
            target: "https://techpana-ai.nextai.asia",
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, ""),
          },
        },
      },
    };
  } else {
    // command === 'build'
    return {
      plugins: [reactPlugin, CopyWidgetScriptPlugin()],
      build: {
        lib: {
          entry: "src/main.tsx",
          name: "Widget",
          // Custom fileName function for JS and CSS
          fileName: (format) => {
            if (format === "iife") return "techpana.widget.iife.min.js";
            return `techpana.widget.${format}.js`;
          },
          formats: ["iife"],
        },
        rollupOptions: {
          output: {
            // Ensure CSS file is named as requested
            assetFileNames: (assetInfo) => {
              if (assetInfo.name && assetInfo.name.endsWith(".css")) {
                return "techpana.widget.style.min.css";
              }
              // Handle image assets
              if (
                assetInfo.name &&
                /\.(png|jpe?g|gif|svg|webp)$/i.test(assetInfo.name)
              ) {
                return "assets/[name][extname]";
              }
              return "[name][extname]";
            },
          },
        },
      },
      define: {
        "process.env.NODE_ENV": JSON.stringify("production"),
        "process.env": "{}",
      },
    };
  }
});
