{"name": "news-widget", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "rm -rf dist && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.13", "postcss": "^8.4.21", "tailwindcss": "^3.3.2", "terser": "^5.43.1", "typescript": "^5.1.3", "vite": "^4.4.9"}}