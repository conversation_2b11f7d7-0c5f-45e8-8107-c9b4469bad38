pipeline {
    agent any

    environment {
        APP_DIR = "${env.WORKSPACE}"
    }

    stages {
        stage('Docker Build') {
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🏗 Building Docker images..."
                            sh 'docker compose build'
                            env.BUILD_SUCCEEDED = "true"
                        } catch (Exception err) {
                            echo "❌ Build failed: ${err}"
                            currentBuild.result = 'FAILURE'
                            env.BUILD_SUCCEEDED = "false"
                        }
                    }
                }
            }
        }

        stage('Rolling Restart Deploy') {
            when {
                expression { env.BUILD_SUCCEEDED == "true" }
            }
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🚀 Rolling restart with new images..."
                            sh 'docker compose up -d'
                            sh 'docker compose ps'
                            
                            echo "✅ Deployment successful"
                        } catch (Exception err) {
                            echo "❌ Deploy failed: ${err}"
                            currentBuild.result = 'FAILURE'
                            throw err
                        }
                    }
                }
            }
        }
    }

    post {
        success {
            echo '✅ Done!'
        }
        failure {
            echo '❌ Failed!'
        }
        always {
            echo "📌 Finished."
        }
    }
}
